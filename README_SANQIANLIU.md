# 《三千流》小说生成器

专为500万字玄幻武侠修仙小说《三千流》定制的智能生成系统，具有爆点控制、章节规划和完整修真体系。

## 系统特点

1. **爆点控制系统** - 每隔固定章节自动生成高潮爆点内容，包括修为突破、强势复仇、身份揭露、奇遇机缘和击败强敌等多种类型
2. **完整修真体系** - 精心设计的五大境界二十五个小阶段的修炼体系，从凡体境到流天境的完整修炼路径
3. **法宝系统** - 多层次法宝体系，包括法器、法宝、灵宝和神器
4. **特殊体质与血脉** - 丰富的特殊体质、血脉、天赋和能力设定
5. **章节结构规划** - 七大阶段的故事架构，从开篇引入到新的征程，合理规划500万字的内容
6. **角色体系** - 自动生成主角、重要角色、敌对势力和盟友势力信息
7. **长篇结构** - 专为500万字超长小说设计，自动控制情节节奏

## 主要特性

- 自动生成小说大纲和章节计划
- 根据章节位置智能调整主角修为进度
- 根据不同阶段智能分配不同类型的爆点
- 提供章节间的连贯性控制
- 批量生成功能，支持断点续传
- 交互式命令行界面，易于使用

## 安装与使用

### 前置条件

- Python 3.7+
- 一个有效的API密钥（支持各种大型语言模型）

### 安装

1. 将代码克隆到本地：

```bash
git clone [仓库地址]
cd novel_generator
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

### 使用方法

1. 命令行交互模式：

```bash
python run_sanqianliu.py interactive
```

2. 直接生成特定内容：

```bash
# 生成小说大纲
python run_sanqianliu.py outline

# 生成角色系统
python run_sanqianliu.py characters

# 生成特定章节
python run_sanqianliu.py chapter 10

# 批量生成章节
python run_sanqianliu.py batch --start 1 --count 10
```

## 修真体系

本系统实现了完整的《三千流》修真体系，共五大境界：

1. **凡体境** - 淬体期、锻骨期、通脉期、易筋期、洗髓期
2. **后天境** - 引气期、聚气期、凝气期、化气期、练气期
3. **先天境** - 筑基期、聚核期、元丹期、金丹期、元婴期
4. **通玄境** - 化神期、返虚期、合道期、大乘期、渡劫期
5. **流天境** - 真流期、化流期、御流期、合流期、道流期

## 故事结构

《三千流》的故事分为七大阶段：

1. **开篇引入** - 主角身世介绍、家族环境描述、母亲遭害经过、获得《三千流》秘籍
2. **初步崛起** - 初步修炼成果、家族内部矛盾、初次展露锋芒、结交初始盟友
3. **踏入修真界** - 接触更大世界、加入宗门历练、结交重要角色、首次大型冒险
4. **实力提升** - 关键战斗历练、探索奇遇秘境、获得重要传承、突破重要境界
5. **仇敌浮现** - 家族仇人现身、阴谋逐渐揭露、敌对势力冲突、遭遇重大危机
6. **最终决战** - 回归复仇计划、集结盟友力量、突破最高境界、大型宗门战争
7. **新的征程** - 尘埃落定后续、建立新的秩序、解决遗留问题、更高层次修炼

## 输出示例

系统将在`output/sanqianliu`目录下生成以下文件：

- `novel_outline.json` - 小说完整大纲
- `character_system.json` - 角色系统信息
- `cultivation_chart.json` - 修炼境界图表
- `story_summary.txt` - 小说概要
- `第X章.txt` - 生成的小说章节
- `checkpoints/` - 批量生成的检查点保存

## 注意事项

1. 请确保有足够的API配额，生成500万字小说需要大量API调用
2. 生成的内容仅供参考，建议人工审核和编辑
3. 生成时建议设置较高的temperature值(0.7-0.8)以增加内容多样性

## 未来计划

1. 加入更多爆点类型和内容模板
2. 优化角色弧线和情感发展
3. 增加战斗场景专用生成模块
4. 添加修真典籍、功法详解生成
5. 开发Web界面，提供更友好的操作体验

## 作者

- 尤川 - 《三千流》主角，也是本系统的"精神作者"

## 许可证

此项目采用MIT许可证 - 详情请参见LICENSE文件 