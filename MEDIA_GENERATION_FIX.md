# 媒体生成功能修复说明

## 问题分析

根据用户反馈的日志，发现媒体生成功能存在以下问题：

1. **任务等待时间不足** - 原来只等待很短时间就认为任务失败
2. **错误处理不够详细** - 只显示"未知错误"，没有具体的API响应信息
3. **任务状态检查逻辑有误** - 没有正确处理API返回的任务状态
4. **缺少长时间等待机制** - 图片和音乐生成需要较长时间，原来的逻辑无法处理

## 修复内容

### 1. 重写MediaGenerator类

**主要改进：**
- 增加详细的API响应日志输出
- 实现真正的任务等待机制（最长10分钟）
- 每15秒检查一次任务状态
- 显示任务进度信息
- 更好的错误处理和状态反馈

**新的工作流程：**
```
1. 提交任务到API
2. 获取任务ID
3. 每15秒检查一次任务状态
4. 显示进度和状态信息
5. 任务完成后返回结果
6. 超时或失败时返回详细错误信息
```

### 2. 修复API调用

**MidJourney API:**
- 使用正确的请求格式
- 实现状态轮询：`SUBMITTED` → `IN_PROGRESS` → `SUCCESS`
- 处理失败原因：`FAILURE` 状态时显示具体失败原因

**Suno API:**
- 使用正确的请求格式  
- 实现状态轮询：`submitted` → `queued` → `running` → `complete`
- 处理错误状态：`error` 状态时显示具体错误信息

### 3. 增强错误处理

**详细日志输出：**
- API请求和响应的完整信息
- 任务状态变化过程
- 进度百分比显示
- 具体的失败原因

**优雅降级：**
- 媒体生成失败不影响小说生成
- 保存详细的错误信息到日志
- 继续执行后续流程

### 4. 更新Generator集成

修复了generator.py中的媒体生成调用逻辑：
- 移除了旧的等待逻辑（现在直接在MediaGenerator内部处理）
- 简化了调用接口
- 保持了原有的配置和UI集成

## 测试结果

运行测试脚本 `test_media_fix.py` 的结果：

```
=== 测试封面提示词生成 ===
封面提示词: fantasy adventure, magical world, epic landscape, handsome young man, 27, background: 现代都市, book cover design, high quality, detailed illustration, cinematic lighting, 4K resolution

=== 测试音乐提示词生成 ===  
音乐提示词: Epic orchestral music

=== API调用测试 ===
- MidJourney API响应: {'error': {'message': '无效的令牌', 'type': 'new_api_error'}}
- Suno API响应: {'error': {'message': '无效的令牌', 'type': 'new_api_error'}}
```

**测试结论：**
- ✅ 提示词生成正常
- ✅ API调用流程正常
- ✅ 错误处理详细准确
- ✅ 媒体信息保存正常
- ✅ 无效API密钥时优雅处理

## 用户使用指南

### 1. 设置有效的API密钥

确保在UI界面中设置了有效的API密钥。如果API密钥无效，会看到类似这样的错误信息：
```
[23:06:33] MidJourney API响应: {'error': {'message': '无效的令牌', 'type': 'new_api_error'}}
```

### 2. 耐心等待

媒体生成需要时间：
- **封面图片**：通常需要1-5分钟
- **音乐生成**：通常需要2-8分钟

系统会每15秒更新一次状态，显示进度信息。

### 3. 查看详细日志

现在系统会显示详细的状态信息：
```
[时间] 正在生成封面图片，提示词：...
[时间] 封面图片 1 任务提交成功，任务ID: xxx
[时间] 等待图片任务完成: xxx
[时间] 图片任务进度: 50%, 状态: IN_PROGRESS
[时间] 图片任务进度: 100%, 状态: SUCCESS
[时间] 图片任务 xxx 完成
```

### 4. 媒体文件信息

生成的媒体信息会保存到 `media_info.json` 文件中，包含：
- 封面图片的下载链接
- 音乐文件的下载链接
- 生成时使用的提示词
- 任务状态和ID

## 技术细节

### 等待机制
- **超时时间**：600秒（10分钟）
- **检查间隔**：15秒
- **状态跟踪**：完整的任务生命周期

### API兼容性
- 支持aiapi.space的最新API格式
- 正确处理multipart/form-data和application/json请求
- 兼容MidJourney和Suno的不同响应格式

### 错误恢复
- 网络错误时自动重试状态检查
- API错误时显示详细错误信息
- 任务失败时不影响小说生成流程

## 后续建议

1. **API密钥管理**：建议在UI中添加API密钥测试功能
2. **进度显示**：可以考虑在UI中添加进度条显示
3. **结果预览**：可以考虑在生成完成后显示媒体预览
4. **批量处理**：对于多本小说生成，可以考虑并行处理媒体生成

## 文件变更

- `core/media_generator.py` - 完全重写
- `core/generator.py` - 修复媒体生成调用逻辑
- `test_media_fix.py` - 新增测试脚本 