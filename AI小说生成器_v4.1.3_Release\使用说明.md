# AI小说生成器

一个基于AI的小说生成工具，可以生成各种类型的小说，支持批量生成、续写等功能。

[![Python 3.7+](https://img.shields.io/badge/python-3.7+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-4.0.0-orange.svg)](VERSION)

## 🌟 功能特点

- **多种小说类型**：支持奇幻冒险、科幻未来、武侠仙侠等14种类型
- **多AI模型支持**：兼容GPT、Claude、Gemini、Moonshot等多种高级模型
- **自定义参数**：可调整温度、核采样、生成长度等参数
- **批量生成**：同时生成多篇不同类型的小说
- **续写功能**：继续创作已有的小说
- **自动保存**：生成过程中自动保存内容，防止数据丢失
- **自定义提示词**：为AI提供个性化创作指导
- **智能重试**：网络问题时自动重试，确保生成过程不中断
- **小说摘要**：自动生成小说摘要，便于管理长篇内容
- **现代界面**：直观的用户界面，简化使用流程

## 📋 系统要求

- Python 3.7 或更高版本
- Windows 10/11, macOS, 或 Linux
- 最小屏幕分辨率: 1200x800
- 内存: 最小 4GB，推荐 8GB+
- 磁盘空间: 最小 500MB（不含生成的小说内容）

## 💻 安装指南

### 方法1: 使用预编译版本

1. 从 [Releases](https://github.com/yourusername/novel_generator/releases) 页面下载最新版本
2. 解压缩文件到任意位置
3. 运行 `novel_generator.exe` (Windows) 或 `novel_generator` (macOS/Linux)

### 方法2: 从源码安装

1. 确保已安装 Python 3.7+
2. 克隆仓库或下载源码：

   ```bash
   git clone https://github.com/yourusername/novel_generator.git
   cd novel_generator
   ```
3. 安装依赖项：

   ```bash
   pip install -r requirements.txt
   ```
4. 运行程序：

   ```bash
   python main.py
   ```

## 🚀 快速开始

1. 首次启动程序时，会提示输入API密钥，请输入有效的AI服务API密钥
2. 在主界面上选择小说类型、目标字数和其他基本参数
3. 如需更详细的控制，点击"高级设置"按钮
4. 点击"开始生成"按钮开始创作
5. 生成过程中可以随时"暂停"或"停止"
6. 完成后，生成的小说将保存在输出目录中

## 📁 项目结构

```
novel_generator/
├── main.py                # 主程序入口
├── main_wrapper.py        # 启动包装器
├── core/                  # 核心功能
│   ├── generator.py       # 小说生成器核心逻辑
│   └── model_manager.py   # 模型管理功能
├── ui/                    # 用户界面
│   ├── app.py             # 主应用界面
│   ├── dialogs.py         # 各种对话框
│   └── assets/            # 界面资源
├── utils/                 # 工具函数
│   ├── config.py          # 配置管理
│   ├── common.py          # 通用工具函数
│   └── logging.py         # 日志功能
└── templates/             # 模板文件
    └── prompts.py         # 小说生成提示词模板
```

## ⚙️ 配置说明

配置项保存在 `novel_generator_config.json`文件中，主要配置项包括：

| 配置项       | 说明                 | 默认值           |
| ------------ | -------------------- | ---------------- |
| API密钥      | 用于访问AI服务的密钥 | -                |
| 模型         | 使用的AI模型         | gemini-2.0-flash |
| 语言         | 生成小说的语言       | 中文             |
| 小说类型     | 生成的小说类型       | 奇幻冒险         |
| 目标字数     | 生成小说的字数       | 20000            |
| 并行数       | 同时生成的小说数量   | 3                |
| 自动保存间隔 | 自动保存的秒数       | 60               |
| 自动摘要间隔 | 自动生成摘要的字数   | 10000            |
| 温度         | 生成随机性参数       | 0.7              |
| 核采样       | top_p参数            | 0.9              |

## 📝 使用场景

1. **个人创作辅助**：快速生成小说初稿，作为创作灵感来源
2. **写作练习**：分析AI生成的小说结构，学习不同类型小说的写作技巧
3. **内容创作**：为博客、网站或游戏生成背景故事
4. **教育用途**：作为写作教学的辅助工具

## 🧩 高级功能

### 自定义提示词

在高级设置中，您可以自定义提示词模板，指导AI按特定风格或结构创作：

```
我想创作一个{类型}故事，主角是{主角}，背景设定在{世界}。
故事应该围绕{主题}展开，包含{情节}元素。
请使用{风格}的写作风格。
```

### 批量生成

在主界面设置"小说数量"大于1，可以同时生成多篇不同类型的小说：

1. 随机类型：每篇小说随机选择不同类型
2. 指定类型：点击"选择多种类型"按钮，为每篇小说单独指定类型

### 续写模式

可以继续创作已有的小说：

1. 单篇续写：选择"续写单个小说"，然后选择要续写的小说文件
2. 批量续写：选择"批量续写文件夹中的小说"，然后选择包含多个小说的文件夹

## 🔍 常见问题

1. **问题**: 生成过程中断了怎么办？**解答**: 程序会自动保存已生成的内容。重启程序后，选择"续写单个小说"功能，找到上次生成的小说文件继续创作。
2. **问题**: 如何修改已生成小说的内容？**解答**: 生成的小说保存为普通文本文件，可以用任何文本编辑器打开修改。修改后可以使用"续写单个小说"功能继续创作。
3. **问题**: API请求频繁失败怎么办？**解答**: 检查API密钥是否正确，网络是否稳定。程序内置了重试机制，但如果持续失败，可以尝试降低"并行数"设置，或更换到其他模型。
4. **问题**: 如何备份配置和已生成的小说？
   **解答**: 配置保存在 `novel_generator_config.json`文件中，小说保存在程序目录下的 `novel_output_时间戳`文件夹中。定期备份这些文件即可。

## 🛠️ 开发者指南

### 环境设置

1. 创建虚拟环境：

   ```bash
   python -m venv venv
   source venv/bin/activate  # 在Windows上使用: venv\Scripts\activate
   ```
2. 安装开发依赖：

   ```bash
   pip install -r requirements.txt
   pip install pytest black flake8 mypy
   ```

### 代码风格

本项目遵循PEP 8代码风格规范。可以使用以下工具检查和格式化代码：

```bash
# 代码风格检查
flake8 .

# 代码格式化
black .

# 类型检查
mypy .
```

### 测试

运行测试套件：

```bash
pytest tests/
```

### 打包

使用PyInstaller创建可执行文件：

```bash
pyinstaller --onedir --windowed --icon=resources/icon.ico --add-data "resources:resources" --name novel_generator main.py
```

## 📄 许可证

本项目仅供学习和研究使用。在使用AI生成内容时，请遵守相关法律法规和服务条款。

## 👥 贡献指南

欢迎贡献代码、报告问题或提出改进建议。贡献前请先查看issue列表，确保不与现有工作重复。

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📞 联系方式

如有任何问题，请通过以下方式联系我们：

- 问题反馈: [GitHub Issues](https://github.com/yourusername/novel_generator/issues)
- 电子邮件: <EMAIL>

---

*祝您创作愉快！*

# AI小说生成器依赖安装说明

## 安装方法

### 方法一：使用安装脚本（推荐）

#### Windows用户
双击运行 `install_dependencies.bat` 文件，按照提示进行安装。

#### 所有平台用户
运行Python安装脚本：
```bash
python install_dependencies.py
```

这两个脚本都会：
1. 检查您的Python环境
2. 安装基础依赖
3. 询问是否要安装PyQt6支持（现代界面）
4. 指导您如何启动程序

### 方法二：手动安装

#### 安装基础依赖
```bash
pip install -r requirements.txt
```

#### 安装PyQt6支持（可选，用于现代界面）
```bash
pip install PyQt6>=6.5.0 PyQt6-Qt6>=6.5.0 PyQt6-sip>=13.5.0
```

## 启动程序

安装完成后，您可以通过以下方式启动程序：

### 方法一：使用启动器（可选择界面）
```bash
python launcher.py
```
或双击 `start_with_ui_choice.bat`（Windows用户）

### 方法二：直接使用原始界面启动
```bash
python main.py
```

### 方法三：直接使用PyQt6界面启动（需已安装PyQt6）
```bash
python -m ui.qt_app
```

## 界面选择

- **原始界面**：基于Tkinter，兼容性好，运行轻量
- **现代界面**：基于PyQt6，美观现代，体验更佳

两种界面功能完全相同，您可以根据喜好选择。

关于PyQt6界面的更多信息，请参阅 [PyQt_UI_README.md](PyQt_UI_README.md)。

## 🔑 获取API密钥

本软件需要API密钥才能访问AI服务生成小说内容。您可以通过以下途径获取API密钥：

1. 访问 [aiapi.space](https://aiapi.space) 官方网站
2. 注册账号并充值
3. 在个人中心获取API密钥
4. 将密钥复制到软件中使用

API计费说明：
- 根据生成字数计费
- 不同模型有不同的费率
- 支持包月无限量套餐

详细价格和套餐信息请查看官网最新公告。

## 📋 版本历史

### v4.0.0 (2023-07-01)
- 新增14种小说类型支持
- 优化模型参数，提升生成质量
- 增强自定义提示词功能
- 支持最新AI大模型

### v3.0.0 (2023-04-15)
- 添加批量生成功能
- 改进用户界面
- 优化生成速度

### v2.0.0 (2023-02-10)
- 支持多种AI模型
- 添加自动保存功能
- 提升稳定性

### v1.0.0 (2023-01-01)
- 首个正式版本发布
