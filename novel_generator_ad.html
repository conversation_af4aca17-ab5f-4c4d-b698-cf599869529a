<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能小说生成器 - 专业级内容创作解决方案</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f8fa;
            font-family: 'Arial', sans-serif;
        }
        
        .container {
            max-width: 100%;
            margin: 20px;
        }
        
        .ad-container {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            overflow: hidden;
            background-color: white;
        }
        
        svg {
            display: block;
            max-width: 100%;
            height: auto;
        }
        
        .download-btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #4a90e2;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .download-btn:hover {
            background-color: #3a7bc8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="ad-container">
            <!-- 嵌入SVG广告 -->
            <?xml version="1.0" encoding="UTF-8" standalone="no"?>
            <svg width="210mm" height="297mm" viewBox="0 0 210 297" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
              <!-- 背景渐变 -->
              <defs>
                <linearGradient id="background-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#f2f7fd;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#e6f0fa;stop-opacity:1" />
                </linearGradient>
                
                <!-- 吉尔福纹样 (Guilloche Pattern) -->
                <pattern id="guilloche-pattern" patternUnits="userSpaceOnUse" width="200" height="200" patternTransform="scale(0.5) rotate(15)">
                  <path d="M100,0 C60,0 60,50 60,50 C60,150 140,150 140,50 C140,50 140,0 100,0" fill="none" stroke="#4a90e2" stroke-width="0.3" opacity="0.3">
                    <animate attributeName="d" dur="30s" repeatCount="indefinite"
                      values="M100,0 C60,0 60,50 60,50 C60,150 140,150 140,50 C140,50 140,0 100,0;
                             M100,0 C70,10 65,40 65,50 C65,140 135,140 135,50 C135,40 130,10 100,0;
                             M100,0 C60,0 60,50 60,50 C60,150 140,150 140,50 C140,50 140,0 100,0"
                      calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
                  </path>
                  
                  <path d="M0,100 C0,60 50,60 50,60 C150,60 150,140 50,140 C50,140 0,140 0,100" fill="none" stroke="#50e3c2" stroke-width="0.3" opacity="0.3" transform="rotate(90 100 100)">
                    <animate attributeName="d" dur="25s" repeatCount="indefinite"
                      values="M0,100 C0,60 50,60 50,60 C150,60 150,140 50,140 C50,140 0,140 0,100;
                             M0,100 C10,70 40,65 50,65 C140,65 140,135 50,135 C40,135 10,130 0,100;
                             M0,100 C0,60 50,60 50,60 C150,60 150,140 50,140 C50,140 0,140 0,100"
                      calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1" />
                  </path>
                  
                  <circle cx="100" cy="100" r="50" fill="none" stroke="#6a67ce" stroke-width="0.2" opacity="0.3">
                    <animate attributeName="r" dur="20s" repeatCount="indefinite"
                      values="50;55;50;45;50"
                      calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1; 0.4 0 0.6 1; 0.4 0 0.6 1" />
                  </circle>
                  
                  <circle cx="100" cy="100" r="70" fill="none" stroke="#6a67ce" stroke-width="0.2" opacity="0.2">
                    <animate attributeName="r" dur="25s" repeatCount="indefinite"
                      values="70;75;70;65;70"
                      calcMode="spline" keySplines="0.4 0 0.6 1; 0.4 0 0.6 1; 0.4 0 0.6 1; 0.4 0 0.6 1" />
                  </circle>
                </pattern>
                
                <!-- 光晕效果 -->
                <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                  <stop offset="0%" style="stop-color:white;stop-opacity:0.7" />
                  <stop offset="100%" style="stop-color:white;stop-opacity:0" />
                </radialGradient>
              </defs>
              
              <!-- 背景 -->
              <rect width="210" height="297" fill="url(#background-gradient)" />
              
              <!-- 吉尔福纹样背景 -->
              <rect width="210" height="297" fill="url(#guilloche-pattern)" opacity="0.8" />
              
              <!-- 装饰边框 -->
              <rect x="10" y="10" width="190" height="277" rx="2" ry="2" fill="none" stroke="#4a90e2" stroke-width="0.5" opacity="0.7" />
              <rect x="12" y="12" width="186" height="273" rx="1" ry="1" fill="none" stroke="#50e3c2" stroke-width="0.3" opacity="0.5" />
              
              <!-- 光晕效果 -->
              <circle cx="105" cy="80" r="70" fill="url(#glow)" opacity="0.3" />
              
              <!-- 标题区域 -->
              <g transform="translate(0,40)">
                <text x="105" y="0" font-family="'Arial', sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#1a3c6e">
                  AI 智能小说生成器
                </text>
                <text x="105" y="12" font-family="'Arial', sans-serif" font-size="8" font-weight="normal" text-anchor="middle" fill="#4a5568">
                  AI NOVEL GENERATOR PRO
                </text>
                
                <line x1="60" y1="22" x2="150" y2="22" stroke="#4a90e2" stroke-width="0.5" opacity="0.7" />
              </g>
              
              <!-- 主要图形 -->
              <g transform="translate(105, 110)">
                <!-- 抽象书本图形 -->
                <rect x="-30" y="-25" width="60" height="70" rx="2" ry="2" fill="#4a90e2" opacity="0.9" />
                <rect x="-28" y="-23" width="56" height="66" rx="1" ry="1" fill="white" opacity="0.9" />
                
                <!-- 抽象文本线条 -->
                <line x1="-20" y1="-15" x2="20" y2="-15" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                <line x1="-20" y1="-8" x2="15" y2="-8" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                <line x1="-20" y1="-1" x2="18" y2="-1" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                <line x1="-20" y1="6" x2="10" y2="6" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                <line x1="-20" y1="13" x2="20" y2="13" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                <line x1="-20" y1="20" x2="15" y2="20" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                <line x1="-20" y1="27" x2="18" y2="27" stroke="#4a90e2" stroke-width="1" opacity="0.6" />
                
                <!-- AI 图标 -->
                <circle cx="30" cy="10" r="15" fill="#50e3c2" opacity="0.9" />
                <text x="30" y="14" font-family="'Arial', sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">AI</text>
              </g>
              
              <!-- 功能描述 -->
              <g transform="translate(0,170)">
                <text x="105" y="0" font-family="'Arial', sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#1a3c6e">
                  专业级AI小说创作解决方案
                </text>
                
                <!-- 功能点 -->
                <g transform="translate(45, 15)">
                  <circle cx="0" cy="0" r="2" fill="#50e3c2" />
                  <text x="7" y="3" font-family="'Arial', sans-serif" font-size="6" fill="#4a5568">批量生成专业小说</text>
                </g>
                
                <g transform="translate(45, 25)">
                  <circle cx="0" cy="0" r="2" fill="#50e3c2" />
                  <text x="7" y="3" font-family="'Arial', sans-serif" font-size="6" fill="#4a5568">多种文风自由选择</text>
                </g>
                
                <g transform="translate(45, 35)">
                  <circle cx="0" cy="0" r="2" fill="#50e3c2" />
                  <text x="7" y="3" font-family="'Arial', sans-serif" font-size="6" fill="#4a5568">智能降AI痕迹技术</text>
                </g>
                
                <g transform="translate(115, 15)">
                  <circle cx="0" cy="0" r="2" fill="#4a90e2" />
                  <text x="7" y="3" font-family="'Arial', sans-serif" font-size="6" fill="#4a5568">一键翻译多语言版本</text>
                </g>
                
                <g transform="translate(115, 25)">
                  <circle cx="0" cy="0" r="2" fill="#4a90e2" />
                  <text x="7" y="3" font-family="'Arial', sans-serif" font-size="6" fill="#4a5568">专业标题自动推荐</text>
                </g>
                
                <g transform="translate(115, 35)">
                  <circle cx="0" cy="0" r="2" fill="#4a90e2" />
                  <text x="7" y="3" font-family="'Arial', sans-serif" font-size="6" fill="#4a5568">优化内容可读性</text>
                </g>
              </g>
              
              <!-- 价格区块 -->
              <g transform="translate(105, 230)">
                <rect x="-40" y="-10" width="80" height="25" rx="5" ry="5" fill="#4a90e2" opacity="0.9" />
                <text x="0" y="0" font-family="'Arial', sans-serif" font-size="7" font-weight="bold" text-anchor="middle" fill="white">
                  限时特惠
                </text>
                <text x="0" y="10" font-family="'Arial', sans-serif" font-size="10" font-weight="bold" text-anchor="middle" fill="white">
                  ¥399
                </text>
              </g>
              
              <!-- 底部联系信息 -->
              <g transform="translate(0, 270)">
                <text x="105" y="0" font-family="'Arial', sans-serif" font-size="6" text-anchor="middle" fill="#4a5568">
                  官方网站: aiapi.club
                </text>
                <text x="105" y="8" font-family="'Arial', sans-serif" font-size="6" text-anchor="middle" fill="#4a5568">
                  联系邮箱: <EMAIL>
                </text>
                <text x="105" y="16" font-family="'Arial', sans-serif" font-size="6" text-anchor="middle" fill="#4a5568">
                  扫描二维码获取免费体验资格
                </text>
              </g>
              
              <!-- 虚拟二维码 -->
              <rect x="90" y="245" width="30" height="30" rx="2" ry="2" fill="#1a3c6e" opacity="0.9" />
              <rect x="95" y="250" width="20" height="20" fill="white" />
              <rect x="100" y="255" width="10" height="10" fill="#1a3c6e" />
              
              <!-- 证书式印章 -->
              <g transform="translate(170, 250)">
                <circle cx="0" cy="0" r="15" fill="none" stroke="#4a90e2" stroke-width="0.5" />
                <circle cx="0" cy="0" r="13" fill="none" stroke="#50e3c2" stroke-width="0.3" />
                <text x="0" y="3" font-family="'Arial', sans-serif" font-size="4" font-weight="bold" text-anchor="middle" fill="#1a3c6e">官方认证</text>
                <text x="0" y="-3" font-family="'Arial', sans-serif" font-size="4" font-weight="bold" text-anchor="middle" fill="#1a3c6e">正版授权</text>
              </g>
            </svg>
        </div>
        <a href="novel_generator_ad.svg" download class="download-btn">下载SVG文件</a>
    </div>
</body>
</html> 